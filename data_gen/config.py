DEFAULT_PARAMS = {
    # 批次数量配置
    "num_train": 200,
    "num_validation": 50,
    "num_test": 1,
    
    # 训练和验证集配置
    "unified_config": {
        "rar_ops_per_batch": 10,                     # 每批次固定包含n个CCG
        "rar_group_size_range": (1, 16),            # 每个CCG的节点数范围
        "flow_size_per_rar_range": (100, 500),       # 每个CCG中流的大小范围 (MB)
        "time_between_rars_range": (0.001, 0.005),          # CCG之间的正常时间间隔范围 (s)
        "burst_probability": 0.3,               # 发生背靠背（爆发）CCG的概率
        "rar_congestion_control": {
            "uplink_congestion_ratio": 1,      # S->P链路拥塞比例(0.0-1.0)
            "downlink_congestion_ratio": 1,    # P->S链路拥塞比例(0.0-1.0)
            "bottleneck_sharing_mode": "concentrated"  # 瓶颈共享模式: "random", "concentrated"
        }
    },
    
    # 测试集独立配置（更具挑战性的测试场景）
    "test_config": {
        "rar_ops_per_batch": 20,                     # 每批次包含5个CCG（更高负载）
        "rar_group_size_range": (1, 8),            # 更大的节点组范围
        "flow_size_per_rar_range": (100,500),       # 更大的流大小范围 (MB)
        "time_between_rars_range": (0.001,0.01),          # 更短的正常间隔（更高并发）
        "burst_probability": 0.3,                # 更高的爆发概率（更极端场景）
        "rar_congestion_control": {
            "uplink_congestion_ratio": 0.8,      # 更高的上行拥塞率
            "downlink_congestion_ratio": 0.8,    # 更高的下行拥塞率  
            "bottleneck_sharing_mode": "concentrated"
        }
    },
    

    
    # 全局配置
    "output_base_dir": "data_gen/datasets",
    "temp_dir": "dataset_temp",
    
    # 拓扑配置
    "topology": {
        "pods": 4,
        "switches_per_pod": 4,
        "hosts_per_switch": 4,
        "ps_bandwidth": 32000, # Mbps (32 Gbps)
        "sh_bandwidth": 32000, # Mbps (32 Gbps)
    },
    
    # 仿真配置
    "simulation": {
        "timestep": 0.001,
        "max_time": 500.0
    }
}

