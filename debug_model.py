#!/usr/bin/env python3
"""
debug_model.py

调试脚本，用于检查模型为什么对所有流预测相同的值
"""
import torch
import numpy as np
import json
import logging
from typing import List, Dict

# 从model.py导入模型组件
from model import (
    STGNNModel, SPATIAL_EMBEDDING_DIM, FUTURE_EMBEDDING_DIM, 
    HIDDEN_DIM, NUM_GNN_LAYERS, DEVICE
)
from clos_topo import Clos

def setup_logging():
    """设置日志记录"""
    logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

def debug_model_predictions():
    """调试模型预测行为"""
    setup_logging()
    
    # 1. 构建拓扑
    logging.info("构建网络拓扑...")
    clos = Clos(4, 4, 4)
    clos.build()
    
    # 2. 加载模型
    logging.info("加载训练好的模型...")
    try:
        checkpoint = torch.load('best_model.pth', map_location=torch.device('cuda' if torch.cuda.is_available() else 'cpu'), weights_only=False)
        
        metadata = checkpoint.get('metadata', {})
        global_mean = metadata.get('global_mean', 0.0)
        global_std = metadata.get('global_std', 1.0)
        hyperparams = checkpoint.get('hyperparams', {
            'spatial_embedding_dim': SPATIAL_EMBEDDING_DIM,
            'future_embedding_dim': FUTURE_EMBEDDING_DIM,
            'hidden_dim': HIDDEN_DIM,
            'num_gnn_layers': NUM_GNN_LAYERS,
        })
        
        logging.info(f"全局统计信息: mean={global_mean:.2f}, std={global_std:.2f}")
        
    except Exception as e:
        logging.error(f"加载模型检查点时出错: {e}")
        return
    
    # 3. 初始化模型
    model = STGNNModel(clos, **hyperparams).to(DEVICE)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    # 更新特征工程器统计信息
    if hasattr(model, 'feature_engineer'):
        model.feature_engineer.global_mean = global_mean
        model.feature_engineer.global_std = global_std
    
    # 4. 加载测试数据
    logging.info("加载测试数据...")
    with open('data_gen/datasets/test/batch_0.json', 'r') as f:
        input_data = json.load(f)
    
    flows = input_data['flows'][:5]  # 只取前5个流进行调试
    
    # 5. 逐个检查流的特征和预测
    logging.info("开始逐个检查流的特征和预测...")
    
    with torch.no_grad():
        for i, flow in enumerate(flows):
            logging.info(f"\n=== 流 {i+1}: {flow['inputs']['flow_id']} ===")
            logging.info(f"流特征: {flow['inputs']['flow_features']}")
            logging.info(f"路径: {flow['inputs']['path']}")
            logging.info(f"开始时间: {flow['inputs']['start_time']}")
            logging.info(f"真实FCT: {flow.get('time_delay', 'N/A')}")
            
            # 准备批次数据
            batch_data = {
                'flows': [flow],
                'all_flows': flows
            }
            
            # 检查特征工程器的输出
            current_time = flow['inputs']['start_time']
            path = flow['inputs']['path']
            
            try:
                # 获取路径链路索引
                path_links = model.link_graph_builder.get_path_link_indices(path)
                logging.info(f"路径链路索引: {path_links}")
                
                # 计算空间特征
                spatial_features = model.feature_engineer.compute_current_spatial_features(current_time, flow, flows)
                logging.info(f"空间特征形状: {spatial_features.shape}")
                logging.info(f"空间特征统计: min={spatial_features.min():.4f}, max={spatial_features.max():.4f}, mean={spatial_features.mean():.4f}")
                
                # 计算未来特征
                future_features = model.feature_engineer.compute_future_temporal_features(path_links, current_time, flows)
                logging.info(f"未来特征形状: {future_features.shape}")
                logging.info(f"未来特征统计: min={future_features.min():.4f}, max={future_features.max():.4f}, mean={future_features.mean():.4f}")
                
                # 计算流特征
                flow_features = model.feature_engineer.extract_flow_features(flow, flows)
                logging.info(f"流特征: {flow_features}")
                
                # 计算增量冲击特征
                proactive_features = model.feature_engineer.compute_proactive_contention_features(flow, flows)
                logging.info(f"增量冲击特征: {proactive_features}")
                
            except Exception as e:
                logging.error(f"特征计算出错: {e}")
                continue
            
            # 模型预测
            try:
                log_predicted_fct = model(batch_data)
                predicted_fct = torch.expm1(log_predicted_fct.squeeze()).item()
                logging.info(f"预测FCT: {predicted_fct:.6f}")
                logging.info(f"对数预测值: {log_predicted_fct.squeeze().item():.6f}")
                
            except Exception as e:
                logging.error(f"模型预测出错: {e}")
                continue
    
    # 6. 检查模型参数是否有梯度或异常值
    logging.info("\n=== 检查模型参数 ===")
    for name, param in model.named_parameters():
        if param.requires_grad:
            logging.info(f"{name}: shape={param.shape}, mean={param.mean():.6f}, std={param.std():.6f}, min={param.min():.6f}, max={param.max():.6f}")
            if torch.isnan(param).any():
                logging.warning(f"参数 {name} 包含NaN值!")
            if torch.isinf(param).any():
                logging.warning(f"参数 {name} 包含无穷值!")

if __name__ == "__main__":
    debug_model_predictions()
